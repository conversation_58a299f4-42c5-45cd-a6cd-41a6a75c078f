// Imagen 3 Generator Functionality
/* global JSZip */ // Declare JSZip as a global variable for ESLint
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const imagenPrompt = document.getElementById('imagenPrompt');
    const imagenNegativePrompt = document.getElementById('imagenNegativePrompt');
    const imagenModel = document.getElementById('imagenModel');
    const imagenCount = document.getElementById('imagenCount');
    const imagenSize = document.getElementById('imagenSize');
    const imagenStyle = document.getElementById('imagenStyle');
    const generateImageBtn = document.getElementById('generateImageBtn');
    const imagenResults = document.getElementById('imagenResults');
    const imagenGallery = document.getElementById('imagenGallery');
    const imagenLoading = document.getElementById('imagenLoading');
    const saveAllImagesBtn = document.getElementById('saveAllImagesBtn');
    const clearImagesBtn = document.getElementById('clearImagesBtn');

    // Image upload and mode elements
    const imagenSourceImage = document.getElementById('imagenSourceImage');
    const imagenImagePreview = document.getElementById('imagenImagePreview');
    const imagenImagePlaceholder = document.getElementById('imagenImagePlaceholder');
    const removeImagenImageBtn = document.getElementById('removeImagenImageBtn');
    const selectImagenImageBtn = document.getElementById('selectImagenImageBtn');
    const imagenImageInput = document.getElementById('imagenImageInput');
    const imagenMode = document.getElementById('imagenMode');
    const imagenEditModeBtn = document.getElementById('imagenEditModeBtn');
    const imagenVariationModeBtn = document.getElementById('imagenVariationModeBtn');
    const promptRequiredText = document.getElementById('promptRequiredText');

    // Add event listener to the model dropdown to toggle UI elements based on model type
    if (imagenModel) {
        // Add event listener to handle UI changes when model changes
        imagenModel.addEventListener('change', function() {
            updateUIBasedOnModel(this.value);
        });

        // Initial UI update based on default selected model
        updateUIBasedOnModel(imagenModel.value);
    }

    // Function to update UI based on selected model
    function updateUIBasedOnModel(modelValue) {
        const isGeminiModel = modelValue.startsWith('gemini');
        const isImagenModel = modelValue.startsWith('imagen');

        // Get UI elements that need to be toggled
        const stylePresetGroup = document.querySelector('.form-group:has(#imagenStyle)');
        const sizeGroup = document.querySelector('.form-group:has(#imagenSize)');

        // Update UI elements visibility based on model type
        if (stylePresetGroup) {
            stylePresetGroup.style.opacity = isImagenModel ? '1' : '0.5';
            const stylePresetLabel = stylePresetGroup.querySelector('label');
            if (stylePresetLabel) {
                stylePresetLabel.textContent = isImagenModel ? 'Style Preset:' : 'Style Preset (Imagen only):';
            }
        }

        if (sizeGroup) {
            sizeGroup.style.opacity = isImagenModel ? '1' : '0.5';
            const sizeLabel = sizeGroup.querySelector('label');
            if (sizeLabel) {
                sizeLabel.textContent = isImagenModel ? 'Size:' : 'Size (Imagen only):';
            }
        }

        // Show appropriate help text
        const helpText = document.getElementById('modelHelpText');
        if (helpText) {
            if (isGeminiModel) {
                helpText.textContent = 'Gemini models are free but have limited control over style and size. They require text output alongside images.';
            } else if (isImagenModel) {
                helpText.textContent = 'Imagen models provide high-quality images with full control over style and size but require a paid API key.';
            }
        }
    }

    // Check if elements exist
    if (!generateImageBtn) {
        console.warn('Imagen generator elements not found');
        return;
    }

    // Get the placeholder element
    const imagenPlaceholder = document.getElementById('imagenPlaceholder');

    // Handle generate button click
    // Check if event listener is already added to prevent duplicates
    if (!generateImageBtn.hasAttribute('data-listener-added')) {
        generateImageBtn.addEventListener('click', function() {
            // Get the current mode
            const mode = imagenMode.value;

            // Check if we have an image or a prompt
            const hasImage = imagenSourceImage && imagenSourceImage.src && !imagenSourceImage.src.endsWith('#');
            const hasPrompt = imagenPrompt.value.trim() !== '';

            // Validate based on mode
            if (mode === 'text-to-image' && !hasPrompt) {
                showToast('Please enter a prompt for text-to-image generation', 'warning');
                return;
            } else if ((mode === 'image-to-image' || mode === 'image-edit') && !hasImage) {
                showToast('Please upload an image for ' + (mode === 'image-edit' ? 'editing' : 'variation'), 'warning');
                return;
            } else if (mode === 'image-edit' && !hasPrompt) {
                showToast('Please enter a prompt describing the edits you want to make', 'warning');
                return;
            }

            // Check if API key is available - using the same API key system as the metadata generator
            const apiKey = getActiveApiKey();
            if (!apiKey) {
                showToast('No API key found. Please add a Gemini API key in Settings', 'error');
                return;
            }

            // Show loading indicator
            imagenLoading.style.display = 'block';
            imagenResults.style.display = 'none';
            if (imagenPlaceholder) {
                imagenPlaceholder.style.display = 'none';
            }

            // Prepare request parameters
            const params = {
                model: imagenModel.value,
                numberOfImages: parseInt(imagenCount.value),
                size: imagenSize.value, // Pass the raw size string (e.g., "1024x768")
                stylePreset: imagenStyle.value, // Always pass the style preset
                mode: mode // Pass the generation mode
            };

            // Add prompt if provided
            if (hasPrompt) {
                params.prompt = imagenPrompt.value.trim();
            }

            // Add source image if provided
            if (hasImage) {
                params.sourceImage = imagenSourceImage.src;
            }

            // Add negative prompt if provided
            if (imagenNegativePrompt.value.trim()) {
                params.negativePrompt = imagenNegativePrompt.value.trim();
            }

            console.log("Prepared parameters:", params);

            // Call the Imagen API using the existing API infrastructure
            generateImagesWithGemini(apiKey, params)
                .then(displayGeneratedImages)
                .catch(error => {
                    console.error('Error generating images:', error);
                    showToast('Error generating images: ' + error.message, 'error');
                    imagenLoading.style.display = 'none';
                });
        });
        // Mark the button as having an event listener
        generateImageBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle save all images button
    if (!saveAllImagesBtn.hasAttribute('data-listener-added')) {
        saveAllImagesBtn.addEventListener('click', function() {
            const images = imagenGallery.querySelectorAll('img');
            if (images.length === 0) {
                showToast('No images to save', 'warning');
                return;
            }

            // Download all images in batch
            downloadAllImages(images);
        });
        // Mark the button as having an event listener
        saveAllImagesBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle send all to metadata button
    const sendAllToMetadataBtn = document.getElementById('sendAllToMetadataBtn');
    if (sendAllToMetadataBtn && !sendAllToMetadataBtn.hasAttribute('data-listener-added')) {
        sendAllToMetadataBtn.addEventListener('click', function() {
            const images = imagenGallery.querySelectorAll('img');
            if (images.length === 0) {
                showToast('No images to send to metadata', 'warning');
                return;
            }

            // Send all images to metadata tab
            sendImagesToMetadata(images);

            // Switch to metadata tab
            const metadataSubtab = document.getElementById('metadata-subtab');
            if (metadataSubtab) {
                metadataSubtab.click();
            }
        });
        // Mark the button as having an event listener
        sendAllToMetadataBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle clear images button
    if (!clearImagesBtn.hasAttribute('data-listener-added')) {
        clearImagesBtn.addEventListener('click', function() {
            imagenGallery.innerHTML = '';
            imagenResults.style.display = 'none';
            if (imagenPlaceholder) {
                imagenPlaceholder.style.display = 'block';
            }
        });
        // Mark the button as having an event listener
        clearImagesBtn.setAttribute('data-listener-added', 'true');
    }

    // Function to handle image upload
    if (selectImagenImageBtn && imagenImageInput) {
        // Check if event listener is already added to prevent duplicates
        if (!selectImagenImageBtn.hasAttribute('data-listener-added')) {
            selectImagenImageBtn.addEventListener('click', function() {
                imagenImageInput.click();
            });
            // Mark the button as having an event listener
            selectImagenImageBtn.setAttribute('data-listener-added', 'true');
        }

        // Check if event listener is already added to prevent duplicates
        if (!imagenImageInput.hasAttribute('data-listener-added')) {
            imagenImageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        // Store the original image data
                        const originalImageData = e.target.result;

                        // Create a thumbnail for API requests (max 800px width/height)
                        createImageThumbnail(originalImageData, 800, function(thumbnailData) {
                            // Store both the original and thumbnail in the image element
                            imagenSourceImage.src = originalImageData; // Display original for preview
                            imagenSourceImage.dataset.thumbnail = thumbnailData; // Store thumbnail for API

                            imagenImagePreview.classList.remove('d-none');
                            imagenImagePlaceholder.classList.add('d-none');

                            // When an image is uploaded, suggest image-to-image mode
                            if (imagenMode.value === 'text-to-image') {
                                imagenMode.value = 'image-to-image';
                                updateUIBasedOnMode();
                            }

                            console.log("Thumbnail created for API requests");
                        });
                    };

                    reader.readAsDataURL(file);
                }
            });
            // Mark the input as having an event listener
            imagenImageInput.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to handle image removal
    if (removeImagenImageBtn) {
        // Check if event listener is already added to prevent duplicates
        if (!removeImagenImageBtn.hasAttribute('data-listener-added')) {
            removeImagenImageBtn.addEventListener('click', function() {
                imagenSourceImage.src = '';
                imagenSourceImage.dataset.thumbnail = ''; // Clear the thumbnail data
                imagenImagePreview.classList.add('d-none');
                imagenImagePlaceholder.classList.remove('d-none');
                imagenImageInput.value = ''; // Clear the file input

                // When image is removed, switch back to text-to-image mode
                imagenMode.value = 'text-to-image';
                updateUIBasedOnMode();
            });
            // Mark the button as having an event listener
            removeImagenImageBtn.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to handle mode selection
    if (imagenMode) {
        // Check if event listener is already added to prevent duplicates
        if (!imagenMode.hasAttribute('data-listener-added')) {
            imagenMode.addEventListener('change', function() {
                updateUIBasedOnMode();
            });
            // Mark the select as having an event listener
            imagenMode.setAttribute('data-listener-added', 'true');
        }

        // Initial UI update based on default selected mode
        updateUIBasedOnMode();
    }

    // Function to handle edit mode button
    if (imagenEditModeBtn) {
        // Check if event listener is already added to prevent duplicates
        if (!imagenEditModeBtn.hasAttribute('data-listener-added')) {
            imagenEditModeBtn.addEventListener('click', function() {
                imagenMode.value = 'image-edit';
                updateUIBasedOnMode();
            });
            // Mark the button as having an event listener
            imagenEditModeBtn.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to handle variation mode button
    if (imagenVariationModeBtn) {
        // Check if event listener is already added to prevent duplicates
        if (!imagenVariationModeBtn.hasAttribute('data-listener-added')) {
            imagenVariationModeBtn.addEventListener('click', function() {
                imagenMode.value = 'image-to-image';
                updateUIBasedOnMode();
            });
            // Mark the button as having an event listener
            imagenVariationModeBtn.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to update UI based on selected mode
    function updateUIBasedOnMode() {
        const mode = imagenMode.value;
        const hasImage = imagenSourceImage && imagenSourceImage.src && !imagenSourceImage.src.endsWith('#');

        // Update prompt requirement text based on mode
        if (promptRequiredText) {
            if (mode === 'image-to-image' && hasImage) {
                promptRequiredText.textContent = '(Optional)';
            } else {
                promptRequiredText.textContent = '(Required)';
            }
        }

        // If mode requires an image but no image is uploaded, show a message
        if ((mode === 'image-to-image' || mode === 'image-edit') && !hasImage) {
            showToast('Please upload an image for ' + (mode === 'image-edit' ? 'editing' : 'variation'), 'info');
        }
    }

    // Function to get active API key - using the same function as in metadata generation
    function getActiveApiKey() {
        // Try to get it from the global window object
        try {
            // Check if we're in the context of the main application
            if (typeof window.getApiKey === 'function') {
                const apiKey = window.getApiKey();
                if (apiKey) {
                    return apiKey;
                }
            }
        } catch (e) {
            console.error("Error accessing window.getApiKey:", e);
        }

        // Check if API key rotation is enabled
        const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

        // If rotation is enabled and we have a getNextApiKey function, try to use it
        if (enableApiKeyRotation && typeof window.getNextApiKey === 'function') {
            try {
                const nextKey = window.getNextApiKey();
                if (nextKey) {
                    console.log("Using next API key from rotation for image generation");
                    return nextKey;
                }
            } catch (e) {
                console.error("Error accessing window.getNextApiKey:", e);
            }
        }



        // Fallback to localStorage directly
        const apiKey = localStorage.getItem('csvision_api_key');
        if (apiKey) {
            return apiKey;
        }

        // Try to get from apiKeys array if it exists
        const apiKeys = JSON.parse(localStorage.getItem('csvision_api_keys') || '[]');
        if (apiKeys.length > 0) {
            const activeKey = apiKeys.find(key => key.active);
            if (activeKey) {
                return activeKey.key;
            } else if (apiKeys[0]) {
                return apiKeys[0].key;
            }
        }

        // Last resort - try the legacy apiKeys array if it exists
        const legacyApiKeys = JSON.parse(localStorage.getItem('apiKeys') || '[]');
        const activeKeyIndex = parseInt(localStorage.getItem('activeApiKeyIndex') || '0');

        if (legacyApiKeys.length > 0 && activeKeyIndex >= 0 && activeKeyIndex < legacyApiKeys.length) {
            return legacyApiKeys[activeKeyIndex].key;
        }

        return null;
    }

    // Function to generate images using Gemini API
    async function generateImagesWithGemini(apiKey, params) {
        try {
            console.log("Generating images with params:", params);

            // Check if this is an Imagen 4 model
            if (params.model && params.model.startsWith('imagen-4')) {
                console.log("Using Imagen 4 API for generation:", params.model);
                return await generateImagesWithImagen4(apiKey, params);
            }

            // For Gemini models, use the fixed model for image generation
            const model = params.model && params.model.startsWith('gemini') ? params.model : "gemini-2.0-flash-preview-image-generation";
            console.log("Using Gemini model for imagen generation:", model);

            // If a different model was requested, log it but still use the fixed model
            if (params.model && params.model !== model && !params.model.startsWith('imagen')) {
                console.log(`Note: Requested model "${params.model}" changed to "${model}" for imagen generation.`);
            }

            const mode = params.mode || "text-to-image";
            const hasSourceImage = params.sourceImage && params.sourceImage.startsWith('data:');

            // Extract image data if we have a source image
            let imageData, mimeType;
            if (hasSourceImage) {
                // Check if we have a thumbnail stored in the image element
                const thumbnailData = imagenSourceImage.dataset.thumbnail;

                // Use thumbnail if available, otherwise use the original image
                const sourceImage = thumbnailData || params.sourceImage;

                // Extract base64 data without the prefix
                imageData = sourceImage.split(',')[1];
                mimeType = sourceImage.split(';')[0].split(':')[1];

                console.log("Using " + (thumbnailData ? "thumbnail" : "original image") + " for API request");
            }

            // Build the prompt with all the parameters if provided
            let fullPrompt = params.prompt || '';

            // If no prompt but we have an image in variation mode, use a default prompt
            if (!fullPrompt && hasSourceImage && mode === 'image-to-image') {
                fullPrompt = "Generate a variation of this image";
            }

            // For image editing, enhance the prompt
            if (mode === 'image-edit' && fullPrompt) {
                fullPrompt = `Edit this image: ${fullPrompt}`;
            }

            // Add negative prompt if provided
            if (params.negativePrompt) {
                fullPrompt += ` Please avoid: ${params.negativePrompt}`;
            }

            // Add style preset if provided
            if (params.stylePreset && params.stylePreset !== 'none') {
                fullPrompt += ` Style: ${params.stylePreset}.`;
            }

            // Prepare the API URL based on the model
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            // Check if parallel processing is enabled
            const enableParallelProcessing = localStorage.getItem('csvision_enable_parallel') === 'true';
            const concurrency = parseInt(localStorage.getItem('csvision_concurrency') || '3');

            console.log(`Image generation using parallel processing: ${enableParallelProcessing ? 'Enabled' : 'Disabled'}, Concurrency: ${concurrency}`);

            // Create requests for each image
            const requests = [];
            for (let i = 0; i < params.numberOfImages; i++) {
                // Add variation number to prompt if generating multiple images
                const variedPrompt = params.numberOfImages > 1 ?
                    `${fullPrompt} (Variation ${i + 1})` : fullPrompt;

                // Create a request body based on the mode
                let requestBody;

                if (mode === 'text-to-image') {
                    // Text-to-image mode - using new Gemini API format
                    requestBody = {
                        model: model,
                        contents: variedPrompt,
                        config: {
                            responseModalities: ["TEXT", "IMAGE"]
                        }
                    };
                } else {
                    // Image-to-image or image-edit mode using new Gemini API format
                    const contents = [];

                    // Add text prompt first
                    if (variedPrompt) {
                        contents.push({ text: variedPrompt });
                    }

                    // Add image if we have one
                    if (hasSourceImage) {
                        contents.push({
                            inlineData: {
                                mimeType: mimeType,
                                data: imageData
                            }
                        });
                    }

                    // Build the request body
                    requestBody = {
                        model: model,
                        contents: contents,
                        config: {
                            responseModalities: ["TEXT", "IMAGE"]
                        }
                    };
                }

                // Add safety settings
                requestBody.safetySettings = [
                    {
                        category: "HARM_CATEGORY_HARASSMENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_HATE_SPEECH",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    }
                ];

                // Store the request
                requests.push({
                    url: apiUrl,
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
            }

            console.log("Preparing API requests with model:", model);

            // Process the results
            const images = [];
            let hasErrors = false;

            // Process requests based on parallel processing setting
            let results = [];
            if (enableParallelProcessing && concurrency > 1) {
                // Use parallel processing with concurrency limit
                const batchSize = Math.min(concurrency, requests.length);
                console.log(`Using parallel processing with batch size: ${batchSize}`);

                // Process in batches
                for (let i = 0; i < requests.length; i += batchSize) {
                    const batch = requests.slice(i, i + batchSize);
                    const batchPromises = batch.map(async (req) => {
                        try {
                            const response = await fetch(req.url, {
                                method: req.method,
                                headers: req.headers,
                                body: req.body
                            });

                            if (!response.ok) {
                                const errorText = await response.text();
                                const errorObj = new Error(`API Error (${response.status}): ${errorText}`);
                                errorObj.status = response.status;
                                errorObj.responseText = errorText;
                                throw errorObj;
                            }

                            return await response.json();
                        } catch (error) {
                            // Check if this is a rate limit error (429)
                            if (error.status === 429) {
                                console.warn("Rate limit exceeded (429) in parallel processing. Attempting to rotate API key...");

                                // IMPORTANT: For imagen generation, we ONLY rotate API keys on failure, NEVER the model.
                                // We always use gemini-2.0-flash-exp-image-generation model for all imagen requests.

                                // Check if API key rotation is enabled
                                const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

                                if (enableApiKeyRotation && typeof window.getNextApiKey === 'function' &&
                                    typeof window.markApiKeyAsFailed === 'function') {

                                    // Mark the current API key as failed
                                    const currentKey = apiKey;
                                    if (currentKey) {
                                        window.markApiKeyAsFailed(currentKey);
                                        console.log("Marked current API key as failed due to rate limit");

                                        // Show toast to inform user
                                        showToast("API rate limit reached. Trying another API key...", "warning");
                                    }

                                    // Try to get the next API key
                                    const nextKey = window.getNextApiKey();
                                    if (nextKey) {
                                        console.log("Obtained next API key from rotation for parallel request");

                                        // Update the API key for this request
                                        apiKey = nextKey;

                                        // Update the request with the new API key
                                        const newUrl = new URL(req.url);
                                        const keyParam = newUrl.searchParams.get('key');
                                        if (keyParam) {
                                            newUrl.searchParams.set('key', nextKey);
                                            req.url = newUrl.toString();

                                            // Retry the request with the new API key
                                            console.log("Retrying parallel request with new API key");
                                            const retryResponse = await fetch(req.url, {
                                                method: req.method,
                                                headers: req.headers,
                                                body: req.body
                                            });

                                            if (!retryResponse.ok) {
                                                const retryErrorText = await retryResponse.text();
                                                throw new Error(`API Error (${retryResponse.status}): ${retryErrorText}`);
                                            }

                                            const retryData = await retryResponse.json();

                                            // Show success toast
                                            showToast("Successfully generated image with new API key", "success");

                                            return retryData;
                                        } else {
                                            console.error("Could not find key parameter in URL");
                                            throw error;
                                        }
                                    } else {
                                        console.warn("No more API keys available for rotation");
                                        throw error;
                                    }
                                } else {
                                    console.warn("API key rotation not enabled or functions not available");
                                    throw error;
                                }
                            } else {
                                // For other errors, just throw
                                throw error;
                            }
                        }
                    });

                    // Wait for current batch to complete before starting next batch
                    const batchResults = await Promise.allSettled(batchPromises);
                    results = results.concat(batchResults);
                }
            } else {
                // Use sequential processing
                console.log('Using sequential processing for image generation');

                // Process requests one by one
                for (const req of requests) {
                    try {
                        const response = await fetch(req.url, {
                            method: req.method,
                            headers: req.headers,
                            body: req.body
                        });

                        if (!response.ok) {
                            const errorText = await response.text();
                            const errorObj = new Error(`API Error (${response.status}): ${errorText}`);
                            errorObj.status = response.status;
                            errorObj.responseText = errorText;
                            throw errorObj;
                        }

                        const data = await response.json();
                        results.push({ status: 'fulfilled', value: data });
                    } catch (error) {
                        // Check if this is a rate limit error (429)
                        if (error.status === 429) {
                            console.warn("Rate limit exceeded (429). Attempting to rotate API key...");

                            // IMPORTANT: For imagen generation, we ONLY rotate API keys on failure, NEVER the model.
                            // We always use gemini-2.0-flash-exp-image-generation model for all imagen requests.

                            // Check if API key rotation is enabled
                            const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

                            if (enableApiKeyRotation && typeof window.getNextApiKey === 'function' &&
                                typeof window.markApiKeyAsFailed === 'function') {

                                // Mark the current API key as failed
                                const currentKey = apiKey;
                                if (currentKey) {
                                    window.markApiKeyAsFailed(currentKey);
                                    console.log("Marked current API key as failed due to rate limit");

                                    // Show toast to inform user
                                    showToast("API rate limit reached. Trying another API key...", "warning");
                                }

                                // Try to get the next API key
                                const nextKey = window.getNextApiKey();
                                if (nextKey) {
                                    console.log("Obtained next API key from rotation");

                                    // Update the API key for this request
                                    apiKey = nextKey;

                                    // Update the request with the new API key
                                    const newUrl = new URL(req.url);
                                    const keyParam = newUrl.searchParams.get('key');
                                    if (keyParam) {
                                        newUrl.searchParams.set('key', nextKey);
                                        req.url = newUrl.toString();

                                        // Retry the request with the new API key
                                        console.log("Retrying request with new API key");
                                        try {
                                            const retryResponse = await fetch(req.url, {
                                                method: req.method,
                                                headers: req.headers,
                                                body: req.body
                                            });

                                            if (!retryResponse.ok) {
                                                const retryErrorText = await retryResponse.text();
                                                throw new Error(`API Error (${retryResponse.status}): ${retryErrorText}`);
                                            }

                                            const retryData = await retryResponse.json();
                                            results.push({ status: 'fulfilled', value: retryData });

                                            // Show success toast
                                            showToast("Successfully generated image with new API key", "success");

                                            continue; // Skip adding the rejected result
                                        } catch (retryError) {
                                            console.error("Retry with new API key also failed:", retryError);
                                            results.push({ status: 'rejected', reason: retryError });
                                        }
                                    } else {
                                        console.error("Could not find key parameter in URL");
                                        results.push({ status: 'rejected', reason: error });
                                    }
                                } else {
                                    console.warn("No more API keys available for rotation");
                                    results.push({ status: 'rejected', reason: error });
                                }
                            } else {
                                console.warn("API key rotation not enabled or functions not available");
                                results.push({ status: 'rejected', reason: error });
                            }
                        } else {
                            // For other errors, just add to results as rejected
                            results.push({ status: 'rejected', reason: error });
                        }
                    }
                }
            }

            // Process each result
            results.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    const responseData = result.value;
                    console.log(`API Response for image ${index + 1}:`, responseData);

                    try {
                        // Process Gemini API response based on the format from the example
                        // Log full response for debugging
                        console.log(`Full response data for image ${index + 1}:`, JSON.stringify(responseData));

                        // Track if we found an image in this response
                        let imageFound = false;

                        // Direct image format - some API versions might return image directly
                        if (responseData.image || responseData.imageData || responseData.data) {
                            const imageData = responseData.image || responseData.imageData || responseData.data;
                            const mimeType = responseData.mimeType || 'image/jpeg';

                            if (imageData) {
                                images.push({
                                    imageBytes: `data:${mimeType};base64,${imageData}`,
                                    mimeType: mimeType
                                });
                                // Found direct image data
                                imageFound = true;
                                console.log(`Found direct image data for image ${index + 1}`);
                            }
                        }

                        // Check if we have candidates - only if we haven't found an image yet
                        if (!imageFound && responseData.candidates && responseData.candidates.length > 0) {
                            const candidate = responseData.candidates[0];
                            console.log(`Examining candidate for image ${index + 1}:`, candidate);

                            // Check if we have content
                            if (candidate.content) {
                                const content = candidate.content;
                                console.log(`Examining content for image ${index + 1}:`, content);

                                // Check if we have parts in the content
                                if (content.parts && content.parts.length > 0) {
                                    console.log(`Found ${content.parts.length} parts for image ${index + 1}`);

                                    // Log all parts for debugging
                                    content.parts.forEach((part, partIndex) => {
                                        console.log(`Part ${partIndex} for image ${index + 1}:`, part);
                                        // Log keys in this part
                                        console.log(`Part ${partIndex} keys:`, Object.keys(part));
                                    });

                                    // Look for image data in the parts
                                    for (const part of content.parts) {
                                        // Check for inlineData (standard format)
                                        if (part.inlineData) {
                                            console.log(`Found inlineData in part for image ${index + 1}`);
                                            // Found image data
                                            images.push({
                                                imageBytes: `data:${part.inlineData.mime_type};base64,${part.inlineData.data}`,
                                                mimeType: part.inlineData.mime_type
                                            });
                                            imageFound = true;

                                            // Log any text that might be included with the image
                                            if (content.parts.some(p => p.text)) {
                                                const textPart = content.parts.find(p => p.text);
                                                console.log(`Image ${index + 1} description:`, textPart.text);
                                            }

                                            break; // We found an image, no need to continue
                                        }

                                        // Alternative format: check for fileData
                                        if (part.fileData) {
                                            console.log(`Found fileData in part for image ${index + 1}`);
                                            // Found image data in fileData format
                                            images.push({
                                                imageBytes: `data:${part.fileData.mimeType};base64,${part.fileData.fileData}`,
                                                mimeType: part.fileData.mimeType
                                            });
                                            imageFound = true;
                                            break;
                                        }

                                        // Another alternative format: check for image object
                                        if (part.image) {
                                            console.log(`Found image object in part for image ${index + 1}:`, part.image);
                                            // Found image data in image format
                                            const mimeType = part.image.mimeType || 'image/jpeg';
                                            const data = part.image.data || part.image.rawBytes || part.image.bytes;

                                            if (data) {
                                                images.push({
                                                    imageBytes: `data:${mimeType};base64,${data}`,
                                                    mimeType: mimeType
                                                });
                                                imageFound = true;
                                                break;
                                            }
                                        }

                                        // Check for inline_data (newer Gemini API format)
                                        if (part.inline_data) {
                                            console.log(`Found inline_data in part for image ${index + 1}`);
                                            // Found image data in inline_data format
                                            images.push({
                                                imageBytes: `data:${part.inline_data.mime_type};base64,${part.inline_data.data}`,
                                                mimeType: part.inline_data.mime_type
                                            });
                                            imageFound = true;
                                            break;
                                        }

                                        // Check for direct base64 data in the part
                                        if (typeof part === 'string' && part.startsWith('data:image/')) {
                                            console.log(`Found direct base64 data for image ${index + 1}`);
                                            // Found direct base64 image data
                                            const mimeType = part.split(';')[0].split(':')[1];
                                            images.push({
                                                imageBytes: part,
                                                mimeType: mimeType
                                            });
                                            imageFound = true;
                                            break;
                                        }
                                    }

                                    // If we didn't find any image data but have text, try to extract image URL from text
                                    if (!imageFound) {
                                        // Look for text parts that might contain image URLs
                                        const textParts = content.parts.filter(p => p.text);
                                        if (textParts.length > 0) {
                                            console.log(`No image data found, but found ${textParts.length} text parts. Checking for image URLs...`);

                                            for (const textPart of textParts) {
                                                // Try to extract image URLs from text
                                                const text = textPart.text;
                                                console.log(`Examining text for image URLs: ${text.substring(0, 100)}...`);

                                                // Check if text contains base64 image data
                                                if (text.includes('data:image/') && text.includes('base64,')) {
                                                    const base64Match = text.match(/data:image\/[^;]+;base64,[a-zA-Z0-9+/=]+/);
                                                    if (base64Match) {
                                                        const base64Data = base64Match[0];
                                                        console.log(`Found base64 image data in text for image ${index + 1}`);
                                                        const mimeType = base64Data.split(';')[0].split(':')[1];
                                                        images.push({
                                                            imageBytes: base64Data,
                                                            mimeType: mimeType
                                                        });
                                                        imageFound = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    // If we didn't find any image data
                                    if (!imageFound) {
                                        console.warn(`No image data found in response for image ${index + 1}`);
                                        hasErrors = true;
                                    }
                                } else {
                                    console.warn(`No parts found in response for image ${index + 1}`);
                                    hasErrors = true;
                                }
                            } else {
                                console.warn(`No content found in candidate for image ${index + 1}`);
                                hasErrors = true;
                            }
                        } else if (!imageFound) {
                            // Check if we have a finishReason that indicates a safety issue
                            if (responseData.candidates &&
                                responseData.candidates[0]) {

                                const finishReason = responseData.candidates[0].finishReason;

                                // Check for safety-related finish reasons
                                if (finishReason === "SAFETY" || finishReason === "IMAGE_SAFETY") {
                                    console.warn(`PROHIBITED CONTENT: Image generation blocked due to safety filters for image ${index + 1}. The prompt contains content that violates Gemini's content policy.`);

                                    // Get more detailed safety information if available
                                    let safetyDetails = "";
                                    if (responseData.candidates[0].safetyRatings &&
                                        responseData.candidates[0].safetyRatings.length > 0) {

                                        const ratings = responseData.candidates[0].safetyRatings;
                                        console.log(`Safety ratings for image ${index + 1}:`, ratings);

                                        // Extract categories that were blocked
                                        const blockedCategories = ratings
                                            .filter(rating => rating.blocked)
                                            .map(rating => rating.category || "UNKNOWN")
                                            .join(", ");

                                        if (blockedCategories) {
                                            safetyDetails = ` Categories: ${blockedCategories}.`;
                                        }
                                    }

                                    showToast(`Your prompt contains prohibited content and has been blocked by safety filters.${safetyDetails} Please use a different prompt.`, 'error');
                                } else if (finishReason === "RECITATION") {
                                    console.warn(`Image generation failed due to recitation issues for image ${index + 1}`);
                                    showToast(`Image generation failed. The prompt may be too similar to existing content. Try a more original prompt.`, 'warning');
                                } else {
                                    console.warn(`Image generation failed with finish reason: ${finishReason} for image ${index + 1}`);
                                    showToast(`Image generation failed: ${finishReason}. Try a different prompt.`, 'warning');
                                }
                            } else {
                                console.warn(`Invalid response format from API for image ${index + 1}`);
                            }
                            hasErrors = true;
                        }
                    } catch (error) {
                        console.error(`Error processing response for image ${index + 1}:`, error);
                        hasErrors = true;
                    }
                } else {
                    console.error(`Error generating image ${index + 1}:`, result.reason);
                    hasErrors = true;
                }
            });

            // If we have at least one successful image, return them
            if (images.length > 0) {
                if (hasErrors) {
                    showToast(`Generated ${images.length} out of ${params.numberOfImages} requested images`, 'warning');
                }
                return images;
            }

            // If all requests failed, fall back to placeholders
            console.warn("Could not generate any images, using placeholders");

            // Check if we have any detailed error information to show
            let errorMessage = 'Could not generate any images. Using placeholders instead.';
            let isSafetyError = false;

            // First check if any successful responses had safety issues
            const safetyIssue = results.find(r =>
                r.status === 'fulfilled' &&
                r.value &&
                r.value.candidates &&
                r.value.candidates[0] &&
                (r.value.candidates[0].finishReason === 'SAFETY' || r.value.candidates[0].finishReason === 'IMAGE_SAFETY')
            );

            if (safetyIssue) {
                errorMessage = "Your prompt contains prohibited content and has been blocked by safety filters. Please use a different prompt.";
                isSafetyError = true;
                console.warn("PROHIBITED CONTENT: Image generation blocked due to safety filters. The prompt contains content that violates Gemini's content policy.");
            } else {
                // Check if any of the results have specific error information
                const errorResults = results.filter(r => r.status === 'rejected');
                if (errorResults.length > 0) {
                    // Log all errors for debugging
                    errorResults.forEach((result, idx) => {
                        console.error(`Error details for request ${idx + 1}:`, result.reason);
                    });

                    // Check for common error patterns
                    const firstError = errorResults[0].reason;

                    if (firstError && firstError.responseText) {
                        try {
                            // Try to parse the error response
                            const errorResponse = JSON.parse(firstError.responseText);
                            console.log("Parsed API error response:", errorResponse);

                            if (errorResponse.error) {
                                if (errorResponse.error.message) {
                                    errorMessage = `API Error: ${errorResponse.error.message}`;
                                    console.error("API error message:", errorResponse.error.message);
                                }

                                // Check for specific error codes
                                if (errorResponse.error.code === 400) {
                                    errorMessage = "Invalid request. The prompt may contain content that violates the API's content policy.";
                                    // This is likely a content policy violation
                                    isSafetyError = true;
                                } else if (errorResponse.error.code === 429) {
                                    errorMessage = "Rate limit exceeded. Please try again later or use a different API key.";
                                } else if (errorResponse.error.code === 403) {
                                    errorMessage = "API access forbidden. Your API key may be invalid or restricted.";
                                }
                            }
                        } catch (e) {
                            console.error("Error parsing API error response:", e);
                            // If we can't parse the JSON, use the raw text
                            if (firstError.responseText.length < 100) {
                                errorMessage = `API Error: ${firstError.responseText}`;
                            }
                        }
                    } else if (firstError && firstError.message) {
                        // Use the error message directly
                        errorMessage = `Error: ${firstError.message}`;
                    }
                }
            }

            // Show toast with the error message
            showToast(errorMessage, isSafetyError ? 'error' : 'warning');

            // Extract width and height from params.size if available
            let width = 1024;
            let height = 1024;

            if (params.size && typeof params.size === 'string') {
                const dimensions = params.size.split('x');
                if (dimensions.length === 2) {
                    width = parseInt(dimensions[0]) || 1024;
                    height = parseInt(dimensions[1]) || 1024;
                }
            }

            return generatePlaceholderImages(params.numberOfImages, width, height, isSafetyError);

        } catch (error) {
            console.error("Error generating images:", error);
            showToast(`Error generating images: ${error.message}`, 'error');

            // Fall back to placeholder images if there's an error
            console.log("Falling back to placeholder images due to error");

            // Check if this is a safety-related error
            let isSafetyError = false;
            if (error.message && (
                error.message.toLowerCase().includes('safety') ||
                error.message.toLowerCase().includes('prohibited') ||
                error.message.toLowerCase().includes('policy violation') ||
                error.message.toLowerCase().includes('content policy')
            )) {
                console.warn("PROHIBITED CONTENT: Error appears to be related to content policy violation");
                isSafetyError = true;
            }

            // Extract width and height from params.size if available
            let width = 1024;
            let height = 1024;

            if (params && params.size && typeof params.size === 'string') {
                const dimensions = params.size.split('x');
                if (dimensions.length === 2) {
                    width = parseInt(dimensions[0]) || 1024;
                    height = parseInt(dimensions[1]) || 1024;
                }
            }

            return generatePlaceholderImages(params.numberOfImages || 1, width, height, isSafetyError);
        }
    }

    // Helper function to generate placeholder images when the API fails
    function generatePlaceholderImages(count = 1, width = 1024, height = 1024, isSafetyError = false) {
        // Ensure parameters are valid
        count = parseInt(count) || 1;
        width = parseInt(width) || 1024;
        height = parseInt(height) || 1024;

        // Ensure reasonable dimensions (between 200 and 2000 pixels)
        width = Math.max(200, Math.min(width, 2000));
        height = Math.max(200, Math.min(height, 2000));

        console.log(`Generating ${count} placeholder images with dimensions ${width}x${height}, safety error: ${isSafetyError}`);

        const images = [];

        // Use different placeholder services based on error type
        let placeholderServices;

        if (isSafetyError) {
            // For safety errors, use a specific placeholder that indicates prohibited content
            placeholderServices = [
                // Red placeholder with warning text
                () => `https://via.placeholder.com/${width}x${height}/ff0000/ffffff?text=PROHIBITED+CONTENT`,

                // Alternative red placeholder
                () => `https://via.placeholder.com/${width}x${height}/ff0000/ffffff?text=CONTENT+POLICY+VIOLATION`
            ];
        } else {
            // For regular errors, use standard placeholders
            placeholderServices = [
                // Picsum Photos - random beautiful images
                (i) => `https://picsum.photos/seed/${Date.now() + i}/${width}/${height}`,

                // Placeholder.com - shows dimensions on the image
                (i) => `https://via.placeholder.com/${width}x${height}?text=Placeholder+Image+${i+1}`,

                // PlaceImg - category-based placeholders
                () => `https://placeimg.com/${width}/${height}/nature/grayscale`
            ];
        }

        for (let i = 0; i < count; i++) {
            // Rotate through the placeholder services
            const serviceIndex = i % placeholderServices.length;
            const imageUrl = placeholderServices[serviceIndex](i);

            images.push({
                imageBytes: imageUrl,
                mimeType: 'image/jpeg',
                isPlaceholder: true, // Mark this as a placeholder image
                isSafetyError: isSafetyError // Mark if this is a safety error
            });
        }
        return images;
    }

    // Function to generate images using Imagen 4 API
    async function generateImagesWithImagen4(apiKey, params) {
        try {
            console.log("Generating images with Imagen 4 API:", params);

            const model = params.model || 'imagen-4.0-generate-preview-06-06';
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateImages?key=${apiKey}`;

            // Prepare the request body for Imagen 4
            const requestBody = {
                model: model,
                prompt: params.prompt || 'A beautiful landscape',
                config: {
                    numberOfImages: params.numberOfImages || 1
                }
            };

            console.log("Imagen 4 request body:", requestBody);

            // Make the API request
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error("Imagen 4 API error:", errorText);
                throw new Error(`Imagen 4 API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log("Imagen 4 response received");

            // Process the response
            const images = [];
            if (data.generatedImages && Array.isArray(data.generatedImages)) {
                for (const generatedImage of data.generatedImages) {
                    if (generatedImage.image && generatedImage.image.imageBytes) {
                        images.push({
                            imageBytes: generatedImage.image.imageBytes,
                            mimeType: 'image/png',
                            isImagen4: true
                        });
                    }
                }
            }

            if (images.length === 0) {
                throw new Error("No images generated by Imagen 4 API");
            }

            console.log(`Imagen 4 generated ${images.length} images`);
            return images;

        } catch (error) {
            console.error("Error in generateImagesWithImagen4:", error);
            throw error;
        }
    }

    // Function to display generated images
    function displayGeneratedImages(images) {
        // Clear previous results
        imagenGallery.innerHTML = '';

        // Create image elements
        images.forEach((image, index) => {
            const col = document.createElement('div');
            col.className = 'col-md-6 mb-2';

            const card = document.createElement('div');
            card.className = 'card';

            // Create image container with position relative for overlay buttons
            const imgContainer = document.createElement('div');
            imgContainer.className = 'position-relative';
            imgContainer.style.flexGrow = '1';

            const img = document.createElement('img');
            img.className = 'card-img-top cursor-pointer';
            img.alt = `Generated image ${index + 1}`;

            // In a real implementation, this would use the base64 data
            // img.src = `data:${image.mimeType};base64,${image.imageBytes}`;
            img.src = image.imageBytes; // For the placeholder implementation
            img.dataset.index = index; // Store the index for reference

            // Add visual indicator for placeholder images
            if (image.isPlaceholder) {
                // Create a placeholder indicator overlay
                const placeholderIndicator = document.createElement('div');

                if (image.isSafetyError) {
                    // Red warning for safety errors
                    placeholderIndicator.className = 'position-absolute top-0 start-0 bg-danger text-white px-2 py-1 m-2 rounded';
                    placeholderIndicator.textContent = 'PROHIBITED CONTENT';
                } else {
                    // Yellow warning for regular placeholders
                    placeholderIndicator.className = 'position-absolute top-0 start-0 bg-warning text-dark px-2 py-1 m-2 rounded';
                    placeholderIndicator.textContent = 'PLACEHOLDER';
                }

                placeholderIndicator.style.opacity = '0.9';
                placeholderIndicator.style.fontSize = '0.8rem';
                placeholderIndicator.style.fontWeight = 'bold';

                // Add it to the container later
                setTimeout(() => {
                    const container = img.closest('.position-relative');
                    if (container) {
                        container.appendChild(placeholderIndicator);
                    }
                }, 100);
            }

            // Add fullscreen button overlay
            const fullscreenBtn = document.createElement('button');
            fullscreenBtn.className = 'btn btn-sm btn-primary-purple text-white position-absolute top-0 end-0 m-1 bi bi-fullscreen';
            fullscreenBtn.title = 'View fullscreen';

            // Add event listener for fullscreen button
            fullscreenBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                openImageFullscreen(img.src);
            });

            // Add event listener for image click to open fullscreen
            img.addEventListener('click', () => {
                openImageFullscreen(img.src);
            });

            // Add elements to image container
            imgContainer.appendChild(img);
            imgContainer.appendChild(fullscreenBtn);

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-2 imagen-card-body';

            const cardTitle = document.createElement('div');
            cardTitle.className = 'd-flex justify-content-between align-items-center';

            const titleText = document.createElement('h6');
            titleText.className = 'card-title mb-0';
            titleText.textContent = `Image ${index + 1}`;

            // Create button group for actions
            const btnGroup = document.createElement('div');
            btnGroup.className = 'btn-group';

            // Add edit button
            const editBtn = document.createElement('button');
            editBtn.className = 'btn btn-sm btn-primary-purple text-white bi bi-pencil';
            editBtn.title = 'Edit this image';
            editBtn.addEventListener('click', () => {
                sendImageToSourceImage(img.src, 'image-edit');
            });

            // Add variant button
            const variantBtn = document.createElement('button');
            variantBtn.className = 'btn btn-sm btn-primary-purple text-white bi bi-shuffle';
            variantBtn.title = 'Create variations of this image';
            variantBtn.addEventListener('click', () => {
                sendImageToSourceImage(img.src, 'image-to-image');
            });

            // Add send to metadata button
            const sendToMetadataBtn = document.createElement('button');
            sendToMetadataBtn.className = 'btn btn-sm btn-success bi bi-arrow-right-circle';
            sendToMetadataBtn.title = 'Send to Metadata';
            sendToMetadataBtn.addEventListener('click', () => {
                sendImageToMetadata(img);
            });

            // Add send to prompter button
            const sendToPrompterBtn = document.createElement('button');
            sendToPrompterBtn.className = 'btn btn-sm btn-info bi bi-chat-square-text';
            sendToPrompterBtn.title = 'Send to Prompter';
            sendToPrompterBtn.addEventListener('click', () => {
                sendImageToPrompter(img.src);
            });

            // Add download button
            const downloadBtn = document.createElement('a');
            downloadBtn.className = 'btn btn-sm btn-primary-purple text-white bi bi-download';
            downloadBtn.title = 'Download image';
            downloadBtn.href = img.src;
            downloadBtn.download = `imagen-${index + 1}.jpg`;

            // Add buttons to group
            btnGroup.appendChild(editBtn);
            btnGroup.appendChild(variantBtn);
            btnGroup.appendChild(sendToMetadataBtn);
            btnGroup.appendChild(sendToPrompterBtn);
            btnGroup.appendChild(downloadBtn);

            cardTitle.appendChild(titleText);
            cardTitle.appendChild(btnGroup);
            cardBody.appendChild(cardTitle);

            card.appendChild(imgContainer);
            card.appendChild(cardBody);
            col.appendChild(card);
            imagenGallery.appendChild(col);
        });

        // Show results and hide loading and placeholder
        imagenResults.style.display = 'block';
        imagenLoading.style.display = 'none';
        if (imagenPlaceholder) {
            imagenPlaceholder.style.display = 'none';
        }
    }

    // Function to create a thumbnail from an image
    function createImageThumbnail(imageDataUrl, maxSize, callback) {
        // Create an image element to load the data URL
        const img = new Image();
        img.onload = function() {
            // Calculate new dimensions while maintaining aspect ratio
            let width = img.width;
            let height = img.height;

            // Only resize if the image is larger than maxSize
            if (width > maxSize || height > maxSize) {
                if (width > height) {
                    height = Math.round(height * (maxSize / width));
                    width = maxSize;
                } else {
                    width = Math.round(width * (maxSize / height));
                    height = maxSize;
                }
            }

            // Create a canvas to draw the resized image
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;

            // Draw the image on the canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, width, height);

            // Get the data URL from the canvas
            const thumbnailDataUrl = canvas.toDataURL(img.src.startsWith('data:image/png') ? 'image/png' : 'image/jpeg', 0.85);

            // Call the callback with the thumbnail data URL
            callback(thumbnailDataUrl);
        };

        // Handle errors
        img.onerror = function() {
            console.error('Error creating thumbnail');
            // Fall back to the original image
            callback(imageDataUrl);
        };

        // Load the image
        img.src = imageDataUrl;
    }

    // Function to open image in fullscreen modal
    function openImageFullscreen(imageSrc) {
        // Check if modal already exists
        let modal = document.getElementById('imagenFullscreenModal');

        // Create modal if it doesn't exist
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'imagenFullscreenModal';
            modal.className = 'modal fade';
            modal.tabIndex = '-1';
            // Remove aria-hidden attribute to fix accessibility issue
            // modal.setAttribute('aria-hidden', 'true');

            const modalDialog = document.createElement('div');
            modalDialog.className = 'modal-dialog modal-dialog-centered modal-xl';

            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';

            const modalHeader = document.createElement('div');
            modalHeader.className = 'modal-header py-2 bg-primary-purple text-white';

            const modalTitle = document.createElement('h5');
            modalTitle.className = 'modal-title';
            modalTitle.textContent = 'Image Preview';

            const closeButton = document.createElement('button');
            closeButton.type = 'button';
            closeButton.className = 'btn-close btn-close-white';
            closeButton.setAttribute('data-bs-dismiss', 'modal');
            closeButton.setAttribute('aria-label', 'Close');

            const modalBody = document.createElement('div');
            modalBody.className = 'modal-body text-center p-0';

            const modalImage = document.createElement('img');
            modalImage.id = 'imagenFullscreenImage';
            modalImage.className = 'img-fluid';
            modalImage.alt = 'Fullscreen preview';

            const modalFooter = document.createElement('div');
            modalFooter.className = 'modal-footer py-1';

            const downloadButton = document.createElement('a');
            downloadButton.id = 'imagenFullscreenDownload';
            downloadButton.className = 'btn btn-primary-purple text-white bi bi-download';
            downloadButton.textContent = ' Download';
            downloadButton.download = 'imagen-download.jpg';

            // Add Edit button
            const editButton = document.createElement('button');
            editButton.id = 'imagenFullscreenEditButton';
            editButton.className = 'btn btn-primary-purple text-white bi bi-pencil me-2';
            editButton.textContent = ' Edit';
            editButton.addEventListener('click', function() {
                // Get the image source
                const imgSrc = modalImage.src;
                if (imgSrc) {
                    // Close the modal
                    bsModal.hide();
                    // Send the image to source image for editing
                    sendImageToSourceImage(imgSrc, 'image-edit');
                }
            });

            // Add Variant button
            const variantButton = document.createElement('button');
            variantButton.id = 'imagenFullscreenVariantButton';
            variantButton.className = 'btn btn-primary-purple text-white bi bi-shuffle me-2';
            variantButton.textContent = ' Variation';
            variantButton.addEventListener('click', function() {
                // Get the image source
                const imgSrc = modalImage.src;
                if (imgSrc) {
                    // Close the modal
                    bsModal.hide();
                    // Send the image to source image for creating variations
                    sendImageToSourceImage(imgSrc, 'image-to-image');
                }
            });

            // Add Send to Prompter button
            const sendToPrompterButton = document.createElement('button');
            sendToPrompterButton.id = 'imagenFullscreenSendToPrompter';
            sendToPrompterButton.className = 'btn btn-success bi bi-arrow-right-circle me-2';
            sendToPrompterButton.textContent = ' Send to Prompter';
            sendToPrompterButton.addEventListener('click', function() {
                // Get the image source
                const imgSrc = modalImage.src;
                if (imgSrc) {
                    // Close the modal
                    bsModal.hide();
                    // Send the image to the prompter tab
                    sendImageToPrompter(imgSrc);
                }
            });

            // Assemble modal
            modalHeader.appendChild(modalTitle);
            modalHeader.appendChild(closeButton);
            modalBody.appendChild(modalImage);
            modalFooter.appendChild(editButton);
            modalFooter.appendChild(variantButton);
            modalFooter.appendChild(sendToPrompterButton);
            modalFooter.appendChild(downloadButton);

            modalContent.appendChild(modalHeader);
            modalContent.appendChild(modalBody);
            modalContent.appendChild(modalFooter);
            modalDialog.appendChild(modalContent);
            modal.appendChild(modalDialog);

            document.body.appendChild(modal);
        }

        // Set image source
        const modalImage = document.getElementById('imagenFullscreenImage');
        const downloadButton = document.getElementById('imagenFullscreenDownload');

        if (modalImage) {
            modalImage.src = imageSrc;
        }

        if (downloadButton) {
            downloadButton.href = imageSrc;
        }

        // Show modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    // Function to send a single image to metadata tab
    async function sendImageToMetadata(imgElement) {
        if (!imgElement || !imgElement.src) {
            showToast('Invalid image', 'error');
            return;
        }

        try {
            // Create a file object from the image
            const response = await fetch(imgElement.src);
            const blob = await response.blob();

            // Create a File object from the blob
            const fileName = `generated-image-${imgElement.dataset.index || Date.now()}.png`;

            // Get the MIME type for logging purposes
            const mimeType = blob.type || 'image/png';
            console.log(`Image MIME type: ${mimeType}`);

            // Convert the image to a proper format for Gemini API
            // Create a canvas to ensure proper image format
            const img = new Image();
            const imgPromise = new Promise((resolve, reject) => {
                img.onload = () => {
                    // Create a canvas to draw the image
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);

                    // Convert to PNG format (most reliable for Gemini API)
                    canvas.toBlob((processedBlob) => {
                        if (processedBlob) {
                            // Create a new File object with PNG type
                            const processedFile = new File([processedBlob], fileName, {
                                type: 'image/png',
                                lastModified: new Date().getTime()
                            });

                            // Add special flag to indicate this is from imagen-subtab
                            processedFile.fromImagen = true;

                            // Add the original source for reference
                            processedFile.originalSrc = imgElement.src;

                            resolve(processedFile);
                        } else {
                            reject(new Error('Failed to convert image to PNG format'));
                        }
                    }, 'image/png', 0.95);
                };
                img.onerror = () => reject(new Error('Failed to load image for conversion'));
            });

            // Load the image
            img.src = imgElement.src;

            // Wait for the conversion to complete
            const file = await imgPromise;

            // Log for debugging
            console.log(`Sending image to metadata tab: ${fileName}`, {
                size: file.size,
                type: file.type,
                fromImagen: file.fromImagen,
                width: img.width,
                height: img.height
            });

            // Add to global images array and metadata table
            if (typeof window.addImageToMetadata === 'function') {
                window.addImageToMetadata(file);
                showToast(`Image sent to metadata tab`, 'success');

                // Switch to metadata tab
                const metadataSubtab = document.getElementById('metadata-subtab');
                if (metadataSubtab) {
                    metadataSubtab.click();
                }
            } else {
                showToast('Could not access metadata system', 'error');
            }
        } catch (error) {
            console.error('Error sending image to metadata:', error);
            showToast(`Error sending image to metadata: ${error.message}`, 'error');
        }
    }

    // Function to send image to prompter tab
    function sendImageToPrompter(imageSrc) {
        if (!imageSrc) {
            showToast('Invalid image', 'error');
            return;
        }

        // Call the function in prompter-generator.js
        if (typeof window.sendImageToPrompter === 'function') {
            window.sendImageToPrompter(imageSrc);
        } else {
            console.error('sendImageToPrompter function not found');
            showToast('Could not send image to Prompter tab', 'error');
        }
    }

    // Function to send image to source image for editing or creating variations
    async function sendImageToSourceImage(imageSrc, mode) {
        if (!imageSrc || !imagenSourceImage) {
            showToast('Invalid image or source image element not found', 'error');
            return;
        }

        try {
            // Fetch the image data
            const response = await fetch(imageSrc);
            const blob = await response.blob();

            // Convert blob to data URL
            const reader = new FileReader();
            reader.onload = function(e) {
                // Store the original image data
                const originalImageData = e.target.result;

                // Create a thumbnail for API requests (max 800px width/height)
                createImageThumbnail(originalImageData, 800, function(thumbnailData) {
                    // Store both the original and thumbnail in the image element
                    imagenSourceImage.src = originalImageData; // Display original for preview
                    imagenSourceImage.dataset.thumbnail = thumbnailData; // Store thumbnail for API

                    // Show the image preview and hide the placeholder
                    imagenImagePreview.classList.remove('d-none');
                    imagenImagePlaceholder.classList.add('d-none');

                    // Set the mode
                    imagenMode.value = mode;
                    updateUIBasedOnMode();

                    // Scroll to the top of the page to show the source image
                    window.scrollTo({ top: 0, behavior: 'smooth' });

                    // Show a toast message
                    showToast(`Image set for ${mode === 'image-edit' ? 'editing' : 'creating variations'}`, 'success');
                });
            };

            reader.onerror = function() {
                throw new Error('Failed to read image data');
            };

            reader.readAsDataURL(blob);
        } catch (error) {
            console.error('Error setting source image:', error);
            showToast(`Error setting source image: ${error.message}`, 'error');
        }
    }

    // Function to send multiple images to metadata tab
    async function sendImagesToMetadata(images) {
        if (!images || images.length === 0) {
            showToast('No images to send', 'warning');
            return;
        }

        let successCount = 0;
        let errorCount = 0;

        // Process each image
        const promises = Array.from(images).map(async (img, index) => {
            try {
                const response = await fetch(img.src);
                const blob = await response.blob();

                // Log blob info for debugging
                console.log(`Batch image blob size: ${blob.size}, type: ${blob.type}`);

                // Create a File object from the blob
                const fileName = `generated-image-${img.dataset.index || Date.now()}-${index + 1}.png`;

                // Convert the image to a proper format for Gemini API
                // Create a canvas to ensure proper image format
                const imgElement = new Image();
                const imgPromise = new Promise((resolve, reject) => {
                    imgElement.onload = () => {
                        // Create a canvas to draw the image
                        const canvas = document.createElement('canvas');
                        canvas.width = imgElement.width;
                        canvas.height = imgElement.height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(imgElement, 0, 0);

                        // Convert to PNG format (most reliable for Gemini API)
                        canvas.toBlob((processedBlob) => {
                            if (processedBlob) {
                                // Create a new File object with PNG type
                                const processedFile = new File([processedBlob], fileName, {
                                    type: 'image/png',
                                    lastModified: new Date().getTime()
                                });

                                // Add special flag to indicate this is from imagen-subtab
                                processedFile.fromImagen = true;

                                // Add the original source for reference
                                processedFile.originalSrc = img.src;

                                resolve(processedFile);
                            } else {
                                reject(new Error('Failed to convert image to PNG format'));
                            }
                        }, 'image/png', 0.95);
                    };
                    imgElement.onerror = () => reject(new Error('Failed to load image for conversion'));
                });

                // Load the image
                imgElement.src = img.src;

                // Wait for the conversion to complete
                const file = await imgPromise;

                // Log for debugging
                console.log(`Sending batch image to metadata tab: ${fileName}`, {
                    size: file.size,
                    type: file.type,
                    fromImagen: file.fromImagen,
                    width: imgElement.width,
                    height: imgElement.height
                });

                // Add to global images array and metadata table
                if (typeof window.addImageToMetadata === 'function') {
                    window.addImageToMetadata(file);
                    successCount++;
                } else {
                    errorCount++;
                    throw new Error('Could not access metadata system');
                }
            } catch (error) {
                console.error('Error sending image to metadata:', error);
                errorCount++;
            }
        });

        // Wait for all promises to complete
        await Promise.allSettled(promises);

        if (successCount > 0) {
            showToast(`Sent ${successCount} image${successCount !== 1 ? 's' : ''} to metadata tab${errorCount > 0 ? ` (${errorCount} failed)` : ''}`, 'success');
        } else if (errorCount > 0) {
            showToast(`Failed to send ${errorCount} image${errorCount !== 1 ? 's' : ''} to metadata tab`, 'error');
        }
    }

    // Function to download all images in batch
    function downloadAllImages(images) {
        if (!images || images.length === 0) {
            showToast('No images to download', 'warning');
            return;
        }

        let successCount = 0;
        let errorCount = 0;
        const timestamp = new Date().toISOString().slice(0, 10);

        // Process each image
        images.forEach((img, index) => {
            try {
                const imageUrl = img.src;

                // Create a download link for the image
                const link = document.createElement('a');
                link.href = imageUrl;
                link.download = `imagen-${index + 1}-${timestamp}.jpg`;

                // Append to body, click, and remove
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                successCount++;
            } catch (error) {
                console.error(`Error downloading image ${index + 1}:`, error);
                errorCount++;
            }
        });

        // Show success message
        if (successCount > 0) {
            showToast(`Downloaded ${successCount} image${successCount !== 1 ? 's' : ''}${errorCount > 0 ? ` (${errorCount} failed)` : ''}`, 'success');
        } else if (errorCount > 0) {
            showToast(`Failed to download ${errorCount} image${errorCount !== 1 ? 's' : ''}`, 'error');
        }
    }
});
